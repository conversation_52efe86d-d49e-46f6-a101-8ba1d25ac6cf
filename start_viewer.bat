@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Excel伤害数据处理工具 - 代码查看器
echo ========================================
echo.

REM 检查HTML文件是否存在
if not exist "main_code_viewer.html" (
    echo ❌ 错误: 找不到 main_code_viewer.html 文件
    echo 请确保HTML文件在当前目录中
    pause
    exit /b 1
)

echo 🚀 正在启动代码查看器...
echo.

REM 方法1: 尝试直接用默认浏览器打开
echo 📝 方法1: 直接打开HTML文件
start "" "main_code_viewer.html"
echo ✅ 已用默认浏览器打开文件
echo.

REM 方法2: 启动Python HTTP服务器
echo 📝 方法2: 启动本地HTTP服务器
echo 🔍 检查Python环境...

python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 找到Python环境
    echo 🌐 启动HTTP服务器...
    echo.
    echo 💡 提示: 按 Ctrl+C 可停止服务器
    echo ========================================
    python run_server.py
) else (
    echo ⚠️  未找到Python环境
    echo 💡 HTML文件已用默认浏览器打开
)

echo.
echo 🎉 感谢使用!
pause