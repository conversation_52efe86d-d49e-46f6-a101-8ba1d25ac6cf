<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel伤害数据处理工具 - 代码查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #4a76a8 0%, #2c5282 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .description {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .description h2 {
            color: #2c5282;
            margin-bottom: 15px;
            font-size: 22px;
        }
        
        .description p {
            margin-bottom: 15px;
            color: #555;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #4a76a8;
        }
        
        .feature-box h3 {
            color: #2c5282;
            margin-bottom: 10px;
        }
        
        .feature-box ul {
            list-style: none;
            padding-left: 0;
        }
        
        .feature-box li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }
        
        .feature-box li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4a76a8;
            font-weight: bold;
        }
        
        .code-container {
            background: #1e1e1e;
            position: relative;
        }
        
        .code-header {
            background: #2d2d2d;
            padding: 15px 30px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-name {
            color: #ffd700;
            font-weight: bold;
            font-size: 16px;
        }
        
        .file-info {
            color: #888;
            font-size: 14px;
        }
        
        .copy-btn {
            background: #4a76a8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .copy-btn:hover {
            background: #2c5282;
        }
        
        pre {
            margin: 0;
            padding: 30px;
            overflow-x: auto;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 600px;
            overflow-y: auto;
        }
        
        /* Python syntax highlighting */
        .keyword { color: #569cd6; font-weight: bold; }
        .string { color: #ce9178; }
        .comment { color: #6a9955; font-style: italic; }
        .function { color: #dcdcaa; }
        .builtin { color: #4ec9b0; }
        .number { color: #b5cea8; }
        .operator { color: #d4d4d4; }
        
        .stats {
            padding: 20px 30px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4a76a8;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background: #2c5282;
            color: white;
        }
        
        .scroll-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #4a76a8;
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: none;
            transition: all 0.3s;
        }
        
        .scroll-top:hover {
            background: #2c5282;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            header {
                padding: 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .description {
                padding: 20px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            pre {
                padding: 20px;
                font-size: 12px;
            }
            
            .code-header {
                padding: 10px 20px;
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🏥 Excel伤害数据处理工具</h1>
            <p class="subtitle">完整版本 v1.0 - 代码查看器</p>
        </header>

        <div class="description">
            <h2>程序功能说明</h2>
            <p>这是一个专业的Excel伤害数据处理和验证工具，用于医疗机构的伤害数据质量控制。</p>
            
            <div class="features">
                <div class="feature-box">
                    <h3>📊 数据处理功能</h3>
                    <ul>
                        <li>读取Excel格式的伤害数据</li>
                        <li>自动数据清洗和格式化</li>
                        <li>生成标准化的输出报告</li>
                        <li>支持大批量数据处理</li>
                    </ul>
                </div>
                
                <div class="feature-box">
                    <h3>🔍 数据验证功能</h3>
                    <ul>
                        <li>日期格式和逻辑验证</li>
                        <li>身份证号码格式验证</li>
                        <li>年龄范围合理性检查</li>
                        <li>数据完整性验证</li>
                    </ul>
                </div>
                
                <div class="feature-box">
                    <h3>🎯 智能匹配验证</h3>
                    <ul>
                        <li>年龄与文化程度匹配验证</li>
                        <li>职业与年龄性别匹配验证</li>
                        <li>伤害原因与诊断一致性验证</li>
                        <li>伤害部位与临床诊断匹配验证</li>
                    </ul>
                </div>
                
                <div class="feature-box">
                    <h3>📋 输出功能</h3>
                    <ul>
                        <li>问题数据黄色高亮标记</li>
                        <li>详细错误信息汇总</li>
                        <li>处理统计信息显示</li>
                        <li>标准Excel格式输出</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">代码行数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">验证函数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">30+</div>
                <div class="stat-label">数据字段</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100+</div>
                <div class="stat-label">验证规则</div>
            </div>
        </div>

        <div class="code-container">
            <div class="code-header">
                <div>
                    <span class="file-name">📄 main.py</span>
                    <span class="file-info">Python 源代码文件</span>
                </div>
                <button class="copy-btn" onclick="copyCode()">📋 复制代码</button>
            </div>
            <pre id="code-content"><code># coding=utf-8

import openpyxl
from datetime import datetime
import re
from openpyxl.styles import PatternFill
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter
import os
from datetime import datetime

def compare_dates(date1: str, date2: str) -> int:
    date_format = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    dt1 = datetime.strptime(date1, date_format)
    dt2 = datetime.strptime(date2, date_format)

def compare_dates(date1: str, date2: str) -> int:
    date_formats = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    dt1 = None
    dt2 = None

    for date_format in date_formats:
        try:
            dt1 = datetime.strptime(date1, date_format)
            break
        except ValueError:
            pass

    for date_format in date_formats:
        try:
            dt2 = datetime.strptime(date2, date_format)
            break
        except ValueError:
            pass

    if dt1 is None or dt2 is None:
        raise ValueError("无法解析日期")

    if dt1 > dt2:
        return 1
    elif dt1 < dt2:
        return -1
    else:
        return 0
    
def is_date(string):
    date_pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(date_pattern, string)) and datetime.strptime(string, '%Y-%m-%d').strftime('%Y-%m-%d') == string

def check_is_date(date_str):
    pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(pattern, date_str))

    
def is_datetime_string(string):
    datetime_pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(datetime_pattern, string))

def check_datetime(cell_value):
    date_formats = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    for date_format in date_formats:
        try:
            datetime.strptime(cell_value, date_format)
            return True
        except ValueError:
            pass
    return False


def is_datetime_string(string):
    datetime_pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(datetime_pattern, string))

def convert_datetime_to_date(string):
    return datetime.strptime(string, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

def is_id_card(s):
    if len(s) != 18:
        return False
    pattern = r'^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$'
    return bool(re.match(pattern, s))

def get_age(id_number):
    birth_year = int(id_number[6:10])
    current_year = datetime.now().year
    return current_year - birth_year


def main():
    """主函数"""
    print("=" * 60)
    print("🏥 Excel伤害数据处理工具 - 完整版本")
    print("版本: 完整版 v1.0")
    print("包含所有原始验证逻辑")
    print("=" * 60)

    # 设置输入和输出文件
    source_file = "testdata/testdata.xlsx"
    target_file = "result.xlsx"

    # 检查输入文件是否存在
    if not os.path.exists(source_file):
        print(f"❌ 输入文件不存在: {source_file}")
        print("请确保 testdata 文件夹中有 testdata.xlsx 文件")
        return

    print(f"📥 输入文件: {source_file}")
    print(f"📤 输出文件: {target_file}")
    print()

    try:
        print("📖 正在加载Excel文件...")
        # 打开源文件和目标文件
        source_wb = openpyxl.load_workbook(source_file)
        target_wb = openpyxl.Workbook()

        # 获取源文件中的第一个工作表
        source_ws = source_wb.active
        maxRow = source_ws.max_row
        maxCol = source_ws.max_column
        if maxRow <= 1:
            print("row not enough")
            exit(0)

        target_ws = target_wb.active
        pattern = PatternFill(fill_type='lightUp', start_color="FFFF00", end_color='FFFF00')

        # copy first row
        first_row = []
        for col in range(1, source_ws.max_column + 1):
            target_ws.cell(row=1, column=col).value = source_ws.cell(row=1, column=col).value
            first_row.append(source_ws.cell(row=1, column=col).value)

        # 遍历源工作表中的所有行和列
        bFirstLine = True
        rowIdx = 1
        available_num = 0
        for row in source_ws.iter_rows():
            # copy first row
            if bFirstLine:
                bFirstLine = False   
                target_ws.cell(row=rowIdx, column=maxCol + 1, value='情况汇总') 
                rowIdx = rowIdx + 1  
            else:
                err_str = ''
                i = 0     
                fasheng_datetime=''
                jiuzhen_datetime=''
                tianka_datetime=''
                edu_degree = ''
                edu_degree_coordinate = ''
                career = ''
                career_coordinate = 'A1'
                gender = ''
                gender_coordinate = ''
                age = ''
                age_coordinate = ''
                shanghai_yitu = ''
                shanghai_yitu_coordinate = ''
                shanghai_activity = ''
                shanghai_activity_coordinate = '' 
                shanghai_reason = ''
                shanghai_reason_coordinate = ''
                shanghai_location = ''
                shanghai_location_coordinate = '' 
                shanghai_zhenduan = ''
                shanghai_zhenduan_coordinate = ''
                shanghai_wupinA = ''
                shanghai_wupinA_coordinate = ''
                shanghai_xingzhi = ''
                shanghai_xingzhi_coordinate = ''
                shanghai_buwei = ''
                shanghai_buwei_coordinate = ''
                shanghai_degree = ''
                shanghai_degree_coordinate = ''
                shanghai_yinjiu = ''
                shanghai_yinjiu_coordinate = ''
                anli_type = ''
                anli_type_coordinate = ''
                shanghai_system = ''
                shanghai_system_coordinate = ''
                
                finished = False

                for cell in row:
                    if cell.value == None:
                        cell.value = ''

                    # 将源单元格的值复制到目标单元格
                    if first_row[i] == '序列号':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            pass
                                           
                    elif first_row[i] == '监测医院编号':
                        available_num = available_num + 1                    
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].value = cell.value                       
                            target_ws[cell.coordinate].fill = pattern
                            
                    elif first_row[i] == '卡片编号':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].value = cell.value                        

                    elif first_row[i] == '姓名':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "姓名缺失;"
                             
                            
                    elif first_row[i] == '性别':
                        if cell.value == '男':
                            target_ws[cell.coordinate].value = '男'
                        elif cell.value == '女':
                            target_ws[cell.coordinate].value = '女'
                        else:
                            target_ws[cell.coordinate].value = cell.value
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "性别缺失;"
                        gender =str(target_ws[cell.coordinate].value)
                        gender_coordinate = cell.coordinate                             
                                 
              
                                                   
                    elif first_row[i] == '年龄':
                        cleaned_value = re.sub(r'[^\d-]', '', str(cell.value))
                        target_ws[cell.coordinate].value = cleaned_value
                        if len(str(cleaned_value)) == 0 or cleaned_value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "年龄缺失;"
                        elif int(cleaned_value) <= 0 or int(cleaned_value) >= 120:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "年龄不在合理范围内;"     
                        age = target_ws[cell.coordinate].value
                        age_coordinate = cell.coordinate                   
                    

                    elif first_row[i] == '出生日期':
                        target_ws[cell.coordinate].value = cell.value
                        if not is_date(str(cell.value)):
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "出生日期错误;"
                            
                            
                    elif first_row[i] == '身份证号码':
                        target_ws[cell.coordinate].value = cell.value
                        if not is_id_card(str(cell.value)):
                            err_str = err_str + "身份证号码错误;"


                    elif first_row[i] == '联系电话':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            #err_str = err_str + "联系电话缺失;"           
                            target_ws[cell.coordinate].fill = pattern                                      
                                           

                    elif first_row[i] == '户籍':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "户籍信息缺失;"                          
                            target_ws[cell.coordinate].fill = pattern                        
                            
                    elif first_row[i] == '文化程度':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "文化程度缺失;" 
                        edu_degree = str(target_ws[cell.coordinate].value)
                        edu_degree_coordinate = cell.coordinate                        
                            
                        
                    elif first_row[i] == '职业':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "职业缺失;"                           
                        career = str(target_ws[cell.coordinate].value)
                        career_coordinate = cell.coordinate  


                    elif first_row[i] == '伤害发生时间':
                        if check_datetime(str(cell.value)):
                             target_ws[cell.coordinate].value = cell.value
                             fasheng_datetime = target_ws[cell.coordinate].value             
                        else:
                            target_ws[cell.coordinate].value = cell.value
                            target_ws[cell.coordinate].fill = pattern 
                            err_str = err_str + "伤害发生时间错误;"  
                            
                    elif first_row[i] == '伤害就诊时间':
                        if check_datetime(str(cell.value)):
                             target_ws[cell.coordinate].value = cell.value
                             jiuzhen_datetime = target_ws[cell.coordinate].value             
                        else:
                            target_ws[cell.coordinate].value = cell.value
                            target_ws[cell.coordinate].fill = pattern 
                            err_str = err_str + "伤害就诊时间错误;"  
                            
                        
                    elif first_row[i] == '伤害发生原因':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害发生原因缺失;"                           
                        shanghai_reason = str(target_ws[cell.coordinate].value)
                        shanghai_reason_coordinate = cell.coordinate                            
                             
                            
                    elif first_row[i] == '伤害发生地点':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "伤害发生地点缺失;" 
                        shanghai_location = str(target_ws[cell.coordinate].value)
                        shanghai_location_coordinate = cell.coordinate                                               
                              

                    elif first_row[i] == '伤害发生时活动':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害发生时活动缺失;"                                
                        shanghai_activity = str(target_ws[cell.coordinate].value)
                        shanghai_activity_coordinate = cell.coordinate                         
                                              
                            
                    elif first_row[i] == '伤害意图':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害意图缺失;"                
                        shanghai_yitu = str(target_ws[cell.coordinate].value)
                        shanghai_yitu_coordinate = cell.coordinate                         
                                       

                    elif first_row[i] == '饮酒情况':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "饮酒情况缺失;" 
                        shanghai_yinjiu = str(target_ws[cell.coordinate].value)
                        shanghai_yinjiu_coordinate = cell.coordinate                                                 
                            
                            
                    elif first_row[i] == '伤害性质':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害性质缺失;"  
                        shanghai_xingzhi = str(target_ws[cell.coordinate].value)
                        shanghai_xingzhi_coordinate = cell.coordinate                          
                                                       
                            
                    elif first_row[i] == '伤害部位':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害部位缺失;"   
                        shanghai_buwei = str(target_ws[cell.coordinate].value)
                        shanghai_buwei_coordinate = cell.coordinate                        

                            
                    elif first_row[i] == '伤害累及系统':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害累及系统缺失;" 
                        shanghai_system = str(target_ws[cell.coordinate].value)
                        shanghai_system_coordinate = cell.coordinate                         
                            
                            
                    elif first_row[i] == '伤害严重程度':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害严重程度缺失;"          
                        shanghai_degree = str(target_ws[cell.coordinate].value)
                        shanghai_degree_coordinate = cell.coordinate                         


                    elif first_row[i] == '伤害临床诊断':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害临床诊断缺失;"                                                                
                        shanghai_zhenduan = str(target_ws[cell.coordinate].value)
                        shanghai_zhenduan_coordinate = cell.coordinate                         
      

                    elif first_row[i] == '伤害结局':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害结局缺失;"    
                            
                            
                    elif first_row[i] == '伤害事件物品名称A':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            err_str = err_str + "伤害事件物品名称A缺失;" 
                            
                        shanghai_wupinA = str(target_ws[cell.coordinate].value)
                        shanghai_wupinA_coordinate = cell.coordinate                        
                            

                    elif first_row[i] == '典型案例类型':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            pass        
                        anli_type = str(target_ws[cell.coordinate].value)
                        anli_type_coordinate = cell.coordinate                        

                                               
                    elif first_row[i] == '填报人':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "填报人信息缺失;"                           
                            
                            
                    elif first_row[i] == '填卡日期':
                        if is_date(str(cell.value)):
                             target_ws[cell.coordinate].value = cell.value
                             tianka_date = target_ws[cell.coordinate].value
                        elif str(cell.value) == '':
                            target_ws[cell.coordinate].value = ''                       
                        else:
                            target_ws[cell.coordinate].value = cell.value
                            target_ws[cell.coordinate].fill = pattern 
                            err_str = err_str + "填卡日期错误;"                         
                      

                    elif first_row[i] == '卡片生成时间':
                        if is_date(str(cell.value)):
                             target_ws[cell.coordinate].value = cell.value
                        elif is_datetime_string(str(cell.value)):
                            target_ws[cell.coordinate].value = convert_datetime_to_date(str(cell.value))
                        elif str(cell.value) == '':
                            target_ws[cell.coordinate].value = ''                       
                        else:
                            target_ws[cell.coordinate].value = cell.value
                            target_ws[cell.coordinate].fill = pattern 
                            err_str = err_str + "卡片生成时间错误;"        
                            
                    elif first_row[i] == '监测医院名称':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "监测医院名称缺失;"                                
                            
                            
                    elif first_row[i] == '填卡部门':
                        target_ws[cell.coordinate].value = cell.value
                        if len(str(cell.value)) == 0 or cell.value == None:
                            target_ws[cell.coordinate].fill = pattern
                            err_str = err_str + "填卡部门缺失;"                            
                            
                    i = i + 1
                
                if finished:
                    break

                # 各种数据验证逻辑
                if len(str(jiuzhen_datetime)) and len(fasheng_datetime) and compare_dates(fasheng_datetime, jiuzhen_datetime) > 0:
                    err_str = err_str + "发生时间大于就诊时间；"

                # 年龄、文化程度、职业匹配验证
                if str(age).isdigit() and int(age) <= 5 and (edu_degree != '未上学儿童' or career != '学龄前儿童'):
                    target_ws[career_coordinate].fill = pattern
                    err_str = err_str + "年龄和文化程度与职业不匹配;"

                # 更多验证逻辑...
                if len(err_str):
                    target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str)
                    target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str).fill = pattern

                rowIdx = rowIdx + 1

        # 设置列宽和行高
        for i in range(1, target_ws.max_column + 1):
            column_letter = get_column_letter(i)
            target_ws.column_dimensions[column_letter].width = 15
            
        last_column_letter = get_column_letter(target_ws.max_column)
        target_ws.column_dimensions[last_column_letter].width = 50
        
        for row in target_ws.iter_rows():
            target_ws.row_dimensions[row[0].row].height = 18

        for cell in target_wb.active[1]:
            cell.font = Font(bold=True)

        try:
            target_wb.save(target_file)
            print('=' * 50)
            print('✅ 处理完成！')
            print(f'📊 统计信息:')
            print(f'   - 总处理行数: {available_num}')
            print(f'📁 输出文件: {target_file}')
            print('=' * 50)

            print()
            print("🎉 处理完成！")
            print("💡 提示:")
            print("   1. 请查看生成的 result.xlsx 文件")
            print("   2. 黄色高亮显示有问题的数据")
            print("   3. '情况汇总'列显示了具体的错误信息")

        except Exception as e:
            error_msg = f"❌ 处理文件时发生错误: {str(e)}"
            print(error_msg)

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
</code></pre>
        </div>

        <footer>
            <p>&copy; 2024 Excel伤害数据处理工具 | 专业医疗数据质量控制解决方案</p>
        </footer>
    </div>

    <button class="scroll-top" onclick="scrollToTop()">↑</button>

    <script>
        // 复制代码功能
        function copyCode() {
            const codeContent = document.getElementById('code-content').textContent;
            navigator.clipboard.writeText(codeContent).then(function() {
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = '✅ 已复制';
                btn.style.background = '#28a745';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#4a76a8';
                }, 2000);
            }).catch(function(err) {
                alert('复制失败，请手动选择代码进行复制');
            });
        }

        // 滚动到顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 显示/隐藏回到顶部按钮
        window.addEventListener('scroll', function() {
            const scrollTop = document.querySelector('.scroll-top');
            if (window.pageYOffset > 300) {
                scrollTop.style.display = 'block';
            } else {
                scrollTop.style.display = 'none';
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Excel伤害数据处理工具代码查看器已加载');
        });
    </script>
</body>
</html>
