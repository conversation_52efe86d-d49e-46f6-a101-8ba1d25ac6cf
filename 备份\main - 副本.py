# coding=utf-8

import openpyxl
from datetime import datetime
import re
from openpyxl.styles import PatternFill
from openpyxl.styles import Font
from openpyxl.utils import get_column_letter
import os
from datetime import datetime

def compare_dates(date1: str, date2: str) -> int:
    date_format = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    dt1 = datetime.strptime(date1, date_format)
    dt2 = datetime.strptime(date2, date_format)

def compare_dates(date1: str, date2: str) -> int:
    date_formats = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    dt1 = None
    dt2 = None

    for date_format in date_formats:
        try:
            dt1 = datetime.strptime(date1, date_format)
            break
        except ValueError:
            pass

    for date_format in date_formats:
        try:
            dt2 = datetime.strptime(date2, date_format)
            break
        except ValueError:
            pass

    if dt1 is None or dt2 is None:
        raise ValueError("无法解析日期")

    if dt1 > dt2:
        return 1
    elif dt1 < dt2:
        return -1
    else:
        return 0
    
def is_date(string):
    date_pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(date_pattern, string)) and datetime.strptime(string, '%Y-%m-%d').strftime('%Y-%m-%d') == string

def check_is_date(date_str):
    pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(pattern, date_str))

    
def is_datetime_string(string):
    datetime_pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(datetime_pattern, string))

def check_datetime(cell_value):
    date_formats = ['%Y-%m-%d %H', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S','%Y-%m-%d %H:%M']
    for date_format in date_formats:
        try:
            datetime.strptime(cell_value, date_format)
            return True
        except ValueError:
            pass
    return False


def is_datetime_string(string):
    datetime_pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(datetime_pattern, string))

def convert_datetime_to_date(string):
    return datetime.strptime(string, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

def is_id_card(s):
    if len(s) != 18:
        return False
    pattern = r'^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$'
    return bool(re.match(pattern, s))

def get_age(id_number):
    birth_year = int(id_number[6:10])
    current_year = datetime.now().year
    return current_year - birth_year


def main():
    """主函数"""
    print("=" * 60)
    print("🏥 Excel伤害数据处理工具 - 完整版本")
    print("版本: 完整版 v1.0")
    print("包含所有原始验证逻辑")
    print("=" * 60)

    # 设置输入和输出文件
    source_file = "testdata/testdata.xlsx"
    target_file = "result.xlsx"

    # 检查输入文件是否存在
    if not os.path.exists(source_file):
        print(f"❌ 输入文件不存在: {source_file}")
        print("请确保 testdata 文件夹中有 testdata.xlsx 文件")
        return

    print(f"📥 输入文件: {source_file}")
    print(f"📤 输出文件: {target_file}")
    print()

    try:
        print("📖 正在加载Excel文件...")
        # 打开源文件和目标文件
        source_wb = openpyxl.load_workbook(source_file)
    target_wb = openpyxl.Workbook()

    # 获取源文件中的第一个工作表
    source_ws = source_wb.active
    maxRow = source_ws.max_row
    maxCol = source_ws.max_column
    if maxRow <= 1:
        print("row not enough")
        exit(0)

    target_ws = target_wb.active
    pattern = PatternFill(fill_type='lightUp', start_color="FFFF00", end_color='FFFF00')

    # copy first row
    first_row = []
    for col in range(1, source_ws.max_column + 1):
        target_ws.cell(row=1, column=col).value = source_ws.cell(row=1, column=col).value
        first_row.append(source_ws.cell(row=1, column=col).value)

    # 遍历源工作表中的所有行和列
    bFirstLine = True
    rowIdx = 1
    available_num = 0
    for row in source_ws.iter_rows():
        # copy first row
        if bFirstLine:
            bFirstLine = False   
            target_ws.cell(row=rowIdx, column=maxCol + 1, value='情况汇总') 
            rowIdx = rowIdx + 1  
        else:
            err_str = ''
            i = 0     
            fasheng_datetime=''
            jiuzhen_datetime=''
            tianka_datetime=''
            edu_degree = ''
            edu_degree_coordinate = ''
            career = ''
            career_coordinate = 'A1'
            gender = ''
            gender_coordinate = ''
            age = ''
            age_coordinate = ''
            shanghai_yitu = ''
            shanghai_yitu_coordinate = ''
            shanghai_activity = ''
            shanghai_activity_coordinate = '' 
            shanghai_reason = ''
            shanghai_reason_coordinate = ''
            shanghai_location = ''
            shanghai_location_coordinate = '' 
            shanghai_zhenduan = ''
            shanghai_zhenduan_coordinate = ''
            shanghai_wupinA = ''
            shanghai_wupinA_coordinate = ''
            shanghai_xingzhi = ''
            shanghai_xingzhi_coordinate = ''
            shanghai_buwei = ''
            shanghai_buwei_coordinate = ''
            shanghai_degree = ''
            shanghai_degree_coordinate = ''
            shanghai_yinjiu = ''
            shanghai_yinjiu_coordinate = ''
            anli_type = ''
            anli_type_coordinate = ''
            shanghai_system = ''
            shanghai_system_coordinate = ''
            
            finished = False

            for cell in row:
                if cell.value == None:
                    cell.value = ''

                # 将源单元格的值复制到目标单元格
                if first_row[i] == '序列号':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        pass
                                       
                elif first_row[i] == '监测医院编号':
                    available_num = available_num + 1                    
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].value = cell.value                       
                        target_ws[cell.coordinate].fill = pattern
                        
                elif first_row[i] == '卡片编号':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].value = cell.value                        

                elif first_row[i] == '姓名':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "姓名缺失;"
                         
                        
                elif first_row[i] == '性别':
                    if cell.value == '男':
                        target_ws[cell.coordinate].value = '男'
                    elif cell.value == '女':
                        target_ws[cell.coordinate].value = '女'
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "性别缺失;"
                    gender =str(target_ws[cell.coordinate].value)
                    gender_coordinate = cell.coordinate                             
                             
          
                                               
                elif first_row[i] == '年龄':
                    cleaned_value = re.sub(r'[^\d-]', '', str(cell.value))
                    target_ws[cell.coordinate].value = cleaned_value
                    if len(str(cleaned_value)) == 0 or cleaned_value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "年龄缺失;"
                    elif int(cleaned_value) <= 0 or int(cleaned_value) >= 120:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "年龄不在合理范围内;"     
                    age = target_ws[cell.coordinate].value
                    age_coordinate = cell.coordinate                   
                

                elif first_row[i] == '出生日期':
                    target_ws[cell.coordinate].value = cell.value
                    if not is_date(str(cell.value)):
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "出生日期错误;"
                        
                        
                elif first_row[i] == '身份证号码':
                    target_ws[cell.coordinate].value = cell.value
                    if not is_id_card(str(cell.value)):
                        err_str = err_str + "身份证号码错误;"


                elif first_row[i] == '联系电话':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        #err_str = err_str + "联系电话缺失;"           
                        target_ws[cell.coordinate].fill = pattern                                      
                                       

                elif first_row[i] == '户籍':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "户籍信息缺失;"                          
                        target_ws[cell.coordinate].fill = pattern                        
                        
                elif first_row[i] == '文化程度':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "文化程度缺失;" 
                    edu_degree = str(target_ws[cell.coordinate].value)
                    edu_degree_coordinate = cell.coordinate                        
                        
                    
                elif first_row[i] == '职业':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "职业缺失;"                           
                    career = str(target_ws[cell.coordinate].value)
                    career_coordinate = cell.coordinate  


                elif first_row[i] == '伤害发生时间':
                    if check_datetime(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                         fasheng_datetime = target_ws[cell.coordinate].value             
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "伤害发生时间错误;"  
                        
                elif first_row[i] == '伤害就诊时间':
                    if check_datetime(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                         jiuzhen_datetime = target_ws[cell.coordinate].value             
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "伤害就诊时间错误;"  
                        
                    
                elif first_row[i] == '伤害发生原因':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害发生原因缺失;"                           
                    shanghai_reason = str(target_ws[cell.coordinate].value)
                    shanghai_reason_coordinate = cell.coordinate                            
                         
                        
                elif first_row[i] == '伤害发生地点':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "伤害发生地点缺失;" 
                    shanghai_location = str(target_ws[cell.coordinate].value)
                    shanghai_location_coordinate = cell.coordinate                                               
                          

                elif first_row[i] == '伤害发生时活动':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害发生时活动缺失;"                                
                    shanghai_activity = str(target_ws[cell.coordinate].value)
                    shanghai_activity_coordinate = cell.coordinate                         
                                          
                        
                elif first_row[i] == '伤害意图':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害意图缺失;"                
                    shanghai_yitu = str(target_ws[cell.coordinate].value)
                    shanghai_yitu_coordinate = cell.coordinate                         
                                   

                elif first_row[i] == '饮酒情况':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "饮酒情况缺失;" 
                    shanghai_yinjiu = str(target_ws[cell.coordinate].value)
                    shanghai_yinjiu_coordinate = cell.coordinate                                                 
                        
                        
                elif first_row[i] == '伤害性质':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害性质缺失;"  
                    shanghai_xingzhi = str(target_ws[cell.coordinate].value)
                    shanghai_xingzhi_coordinate = cell.coordinate                          
                                                   
                        
                elif first_row[i] == '伤害部位':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害部位缺失;"   
                    shanghai_buwei = str(target_ws[cell.coordinate].value)
                    shanghai_buwei_coordinate = cell.coordinate                        

                        
                elif first_row[i] == '伤害累及系统':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害累及系统缺失;" 
                    shanghai_system = str(target_ws[cell.coordinate].value)
                    shanghai_system_coordinate = cell.coordinate                         
                        
                        
                elif first_row[i] == '伤害严重程度':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害严重程度缺失;"          
                    shanghai_degree = str(target_ws[cell.coordinate].value)
                    shanghai_degree_coordinate = cell.coordinate                         


                elif first_row[i] == '伤害临床诊断':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害临床诊断缺失;"                                                                
                    shanghai_zhenduan = str(target_ws[cell.coordinate].value)
                    shanghai_zhenduan_coordinate = cell.coordinate                         
  

                elif first_row[i] == '伤害结局':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害结局缺失;"    
                        
                        
                elif first_row[i] == '伤害事件物品名称A':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        err_str = err_str + "伤害事件物品名称A缺失;" 
                        
                    shanghai_wupinA = str(target_ws[cell.coordinate].value)
                    shanghai_wupinA_coordinate = cell.coordinate                        
                        

                elif first_row[i] == '典型案例类型':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        pass        
                    anli_type = str(target_ws[cell.coordinate].value)
                    anli_type_coordinate = cell.coordinate                        

                                           
                elif first_row[i] == '填报人':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "填报人信息缺失;"                           
                        
                        
                elif first_row[i] == '填卡日期':
                    if is_date(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                         tianka_date = target_ws[cell.coordinate].value
                    elif str(cell.value) == '':
                        target_ws[cell.coordinate].value = ''                       
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "填卡日期错误;"                         
                  

                elif first_row[i] == '卡片生成时间':
                    if is_date(str(cell.value)):
                         target_ws[cell.coordinate].value = cell.value
                    elif is_datetime_string(str(cell.value)):
                        target_ws[cell.coordinate].value = convert_datetime_to_date(str(cell.value))
                    elif str(cell.value) == '':
                        target_ws[cell.coordinate].value = ''                       
                    else:
                        target_ws[cell.coordinate].value = cell.value
                        target_ws[cell.coordinate].fill = pattern 
                        err_str = err_str + "卡片生成时间错误;"        
                        
                elif first_row[i] == '监测医院名称':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "监测医院名称缺失;"                                
                        
                        
                elif first_row[i] == '填卡部门':
                    target_ws[cell.coordinate].value = cell.value
                    if len(str(cell.value)) == 0 or cell.value == None:
                        target_ws[cell.coordinate].fill = pattern
                        err_str = err_str + "填卡部门缺失;"                            
                        
                i = i + 1
            
            if finished:
                break

            if len(str(jiuzhen_datetime)) and len(fasheng_datetime) and compare_dates(fasheng_datetime, jiuzhen_datetime) > 0:
                err_str = err_str + "发生时间大于就诊时间；"


            if str(age).isdigit() and int(age) <= 5 and (edu_degree != '未上学儿童' or career != '学龄前儿童'):
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "年龄和文化程度与职业不匹配;"         
            if 5 < str(age).isdigit() and int(age) < 7 and edu_degree != '小学' and career not in ['在校学生','其他/不清楚']:
                target_ws[career_coordinate].fill = pattern   
                err_str = err_str + "年龄和文化程度与职业不匹配;"  
            if 7 <= str(age).isdigit() and int(age) < 16 and (edu_degree != '小学'or edu_degree != '初中') and career not in ['在校学生','其他/不清楚']:
                target_ws[career_coordinate].fill = pattern   
                err_str = err_str + "年龄和文化程度与职业不匹配;"  
            if 16 <= str(age).isdigit() and int(age) < 18 and edu_degree not in  ['大专','大学及以上'] and career in ['学龄前儿童','离退休人员','军人']:
                target_ws[career_coordinate].fill = pattern   
                err_str = err_str + "年龄和文化程度与职业不匹配;"  
            
            
            if 17 <= str(age).isdigit() and int(age) <= 21 and edu_degree == '大专' and career != '在校学生':
                target_ws[career_coordinate].fill = pattern   
                err_str = err_str + "年龄和文化程度与职业不匹配;"  
            if 17 <= str(age).isdigit() and int(age) <= 22 and edu_degree == '大学及以上' and career != '在校学生':
                target_ws[career_coordinate].fill = pattern   
                err_str = err_str + "年龄和文化程度与职业不匹配;"                 
                
            
            if 18 <= str(age).isdigit() and int(age) and edu_degree != '未上学儿童' and career != '学龄前儿童':
                pass
            if str(age).isdigit() and int(age) >= 18 and career == '军人' and career not in ['未上学儿童','文盲/半文盲','小学']:
                pass
            if career == '在校学生' and career != '文盲/半文盲':
                pass
                

            if gender == '男' and str(age).isdigit() and int(age) >= 50 and career == '在校学生':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "性别年龄与职业不匹配;" 
            if gender == '女' and str(age).isdigit() and int(age) >= 45 and career == '在校学生':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "性别年龄与职业不匹配;"                    
            if gender == '男' and str(age).isdigit() and int(age) < 50 and career == '离退休人员':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "性别年龄与职业不匹配;" 
            if gender == '女' and str(age).isdigit() and int(age) < 45 and career == '离退休人员':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "性别年龄与职业不匹配;" 


            if str(age).isdigit() and int(age) < 10 and shanghai_yitu == '自伤/自杀':
                target_ws[shanghai_yitu_coordinate].fill = pattern
                err_str = err_str + "年龄不符合伤害意图;"       


            if edu_degree == '文盲/半文盲' and str(age).isdigit() and int(age) < 15:
                target_ws[edu_degree_coordinate].fill = pattern
                err_str = err_str + "文化程度和年龄不匹配;"   
            if edu_degree == '小学' and str(age).isdigit() and int(age) < 5:
                target_ws[edu_degree_coordinate].fill = pattern
                err_str = err_str + "文化程度和年龄不匹配;"  
            if (edu_degree == '初中' or edu_degree == '高中/中专') and str(age).isdigit() and int(age) < 10:
                target_ws[edu_degree_coordinate].fill = pattern
                err_str = err_str + "文化程度和年龄不匹配;"  
            if (edu_degree == '大专' or edu_degree == '大学及以上') and str(age).isdigit() and int(age) < 15:
                target_ws[edu_degree_coordinate].fill = pattern
                err_str = err_str + "文化程度和年龄不匹配;"  
                
                
            if str(age).isdigit() and int(age) <= 5 and edu_degree != '未上学儿童' and career != '学龄前儿童':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "年龄和文化程度与职业不匹配;"                 
                
             
            if career in ['学龄前儿童','离退休人员','在校学生','家务','待业'] and shanghai_activity == '工作':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "职业和伤害发生活动不匹配;"              
            if career != '专业技术人员' and str(age).isdigit() and str(age).isdigit() and int(age) < 16 and shanghai_activity == '工作':
                target_ws[career_coordinate].fill = pattern
                err_str = err_str + "职业和伤害发生活动不匹配;"               
            

            if shanghai_reason in ['机动车交通伤','非机动车交通伤'] and shanghai_location != '公路/街道':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害发生地点不符;"      
            if shanghai_reason in ['机动车交通伤','非机动车交通伤'] and shanghai_activity == '家务':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害发生活动不符;"                              
            if shanghai_reason in ['跌落','坠落'] and shanghai_activity == '驾乘交通工具':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害发生时活动不符;"                     
            

            if shanghai_reason == '火器伤' and '枪' not in shanghai_zhenduan:
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和临床诊断不符;"   
            if shanghai_reason == '火器伤' and shanghai_wupinA != '弹药':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害发生物品A不符;"                   
                     
            keywords = ['烧','烫','晒','电击','灼']    
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_reason != '烧烫伤' :
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断和伤害发生原因不符44;"                                     
   
            if shanghai_reason == '烧烫伤' and shanghai_xingzhi != '烧烫伤':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害性质不符;"                
                
                
            if shanghai_reason == '动物伤' and shanghai_yitu == '他人故意':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因与伤害意图不符1;" 
            if shanghai_reason == '性侵犯' and shanghai_yitu != '他人故意':
                target_ws[shanghai_reason_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因与伤害意图不符2;"                 
            if shanghai_reason == '中毒' and shanghai_xingzhi != '内脏器官伤':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因与伤害性质不匹配;" 
            if shanghai_reason == '中毒' and shanghai_buwei != '全身广泛受伤':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因与伤害部位不匹配;"                            
            

            keywords = ['出租车', '公交车', '机动车', '轿车', '汽车', '小轿车', '小汽车', '大巴车', '面包车', '的士车', '货柜车', '泥头车', '小货车', '巴士', '叉车', '的士']
            if any(keyword in shanghai_wupinA for keyword in keywords) and shanghai_reason != '机动车交通伤':
                target_ws[shanghai_wupinA_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害物品名称A不符1;"    
             

            keywords = ['单车','电单车','电动车','电动自行车','非机动车','自行车','三轮车','电动三轮车','电瓶车']
            if any(keyword in shanghai_wupinA for keyword in keywords) and shanghai_reason != '非机动车交通伤':
                target_ws[shanghai_wupinA_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害物品名称A不符2;"                          
                  
            keywords = ['竹叶青','猪','蜘蛛','蟑螂','鱼刺','鱼','鹦鹉','羊驼','小龙虾','小白鼠','蜈蚣','乌龟','蚊子','蚊虫','王八','蛙','豚鼠','兔子牙齿','兔子','兔','跳蚤','松鼠','水母','水貂','鼠','珊瑚','蜱虫','螃蟹钳子','螃蟹','蜜蜂','蜜袋鼯','猫','蚂蚁','马蜂','马','龙虾','龙猫','流浪猫','流浪狗','梨花猫','老鼠咬伤','老鼠牙齿','老鼠','昆虫','节肢动物','甲鱼','黄蜂','狐狸','猴子','猴','红火蚂蚁','海蜇','海虾','海鲶鱼','海螺','海葵','海胆','龟','狗爪','狗咬伤','狗','蜂','飞虫','动物','宠物猪','宠物猫','宠物狗','虫子','虫咬伤','虫','仓鼠','不明虫类','蝙蝠','蜜蜂']
            if any(keyword in shanghai_wupinA for keyword in keywords) and shanghai_reason != '动物伤':
                target_ws[shanghai_wupinA_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害物品名称A不符3;"                        
                  
            keywords = ['剪刀','鱼钩','碎玻璃','玻璃瓶','水果刀','菜刀','刀']
            if any(keyword in shanghai_wupinA for keyword in keywords) and shanghai_reason != '锐器伤':
                target_ws[shanghai_wupinA_coordinate].fill = pattern
                err_str = err_str + "伤害发生原因和伤害物品名称A不符4;"                         
                

            if shanghai_xingzhi == '骨折' and ('骨折' not in str(shanghai_zhenduan)) and shanghai_degree =='轻度':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害性质骨折与临床诊断和伤害严重程度不匹配;"  
                
                
            keywords = ['脑震荡','头部的损伤','蛛网膜下出血','硬膜下血肿','硬膜下出血']
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei == '头颈部' and shanghai_xingzhi != '脑震荡':             
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害性质与临床诊断和伤害部位不匹配;"  
                

            if '骨折' in shanghai_zhenduan and shanghai_xingzhi != '骨折':
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "伤害诊断结果骨折与伤害性质不符;"  
            elif '骨折' not in shanghai_zhenduan and '裂伤' in shanghai_zhenduan and shanghai_xingzhi != '锐器伤/开放伤':
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "伤害诊断裂伤与伤害性质不符;"  
            elif '骨折' not in shanghai_zhenduan and '裂伤' not in shanghai_zhenduan and '挫伤' in str(shanghai_zhenduan) and shanghai_xingzhi != '挫伤/擦伤':
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "伤害诊断挫伤与伤害性质不符;" 
            elif '骨折' not in shanghai_zhenduan and '裂伤' not in shanghai_zhenduan and '挫伤' not in str(shanghai_zhenduan) and '扭' in str(shanghai_zhenduan) and shanghai_xingzhi != '扭伤/拉伤':
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "伤害诊断扭与伤害性质不符;" 


            if shanghai_xingzhi == '骨折' and shanghai_zhenduan.count('骨折') > 1 and shanghai_buwei != '多部位':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害部位与伤害诊断结果多处骨折不符;"                 

            if '骨折' not in shanghai_zhenduan and '裂伤' in shanghai_zhenduan and shanghai_zhenduan.count('裂伤') > 1 and shanghai_buwei != '多部位':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害诊断与伤害部位不符;"  
            if '骨折' not in shanghai_zhenduan and '裂伤' not in shanghai_zhenduan and '挫伤' in shanghai_zhenduan and shanghai_zhenduan.count('挫伤') > 1 and shanghai_buwei != '多部位':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害诊断与伤害部位不符;" 
            if '骨折' not in shanghai_zhenduan and '裂伤' not in shanghai_zhenduan and '挫伤' not in shanghai_zhenduan and '扭' in shanghai_zhenduan and shanghai_zhenduan.count('扭') >1 and shanghai_buwei != '多部位':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害诊断与伤害部位不符;"                  


            if '外伤' in shanghai_zhenduan and shanghai_xingzhi == '扭伤/拉伤':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害诊断结果包含外伤与伤害性质不符;"              
            if '酒精中毒' in shanghai_zhenduan and shanghai_activity == '生命活动' and shanghai_yinjiu != '饮用':
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "伤害诊断与伤害发生时活动和饮酒情况不匹配;"               
             

            if anli_type == '严重非故意' and shanghai_degree !='重度' and shanghai_yitu == '非故意':
                target_ws[anli_type_coordinate].fill = pattern
                err_str = err_str + "典型案例类型严重非故意、伤害严重程度和伤害意图不匹配;" 
            if anli_type == '重度非故意' and shanghai_degree !='重度' and shanghai_yitu != '非故意':
                target_ws[anli_type_coordinate].fill = pattern
                err_str = err_str + "典型案例类型重度非故意、伤害严重程度和伤害意图不匹配;"    
           
            keywords = ['狗','猫','蛇','虫','蜂','鼠','兔','猴','龟','动物']
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_xingzhi != '叮/咬/抓伤':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断与伤害性质不符；"                 
             
            keywords = ['头','面','鼻','唇','耳','下颌','上颌','颈','口腔','牙','眉','颅','脑','眼','额']        
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei != '头颈部':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断与伤害部位不符1;"                    
             
             
            keywords = ['锁骨','肩胛骨','肱骨','手','上肢','肩','上臂','肘','前臂','腕','拇指','食指','中指','无名指','尾指','尺骨','桡骨','舟状骨','掌骨','指骨','环指']        
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei != '上肢':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断与伤害部位不符2;"              
             
                
            keywords = ['腿','脚','踝','下肢','趾','足','膝','臀','跟骨','跟腱','跖骨','距骨','跟距','髌骨','腓骨','股骨','胫骨']       
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei != '下肢':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断与伤害部位不符3;"               
                
            keywords = ['胸', '肋骨', '脊柱', '骨盆','背','腹','骶','髋','腰','生殖器', '会阴', '外阴','阴唇','阴囊', '睾丸','阴茎','心','肾','膀胱']        
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei != '躯干':
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断与伤害部位不符4;"

            if '不清楚' in shanghai_zhenduan:
                target_ws[shanghai_zhenduan_coordinate].fill = pattern
                err_str = err_str + "不能明确的伤害部位;"  

            keywords = ['中毒', '触电', '冻伤','窒息']
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_buwei != '全身广泛受伤':           
                target_ws[shanghai_buwei_coordinate].fill = pattern
                err_str = err_str + "伤害部位和伤害临床诊断不匹配;"     
                

            if shanghai_xingzhi == '烧烫伤' and shanghai_degree != '轻度' and shanghai_system != '皮肤':
                target_ws[shanghai_system_coordinate].fill = pattern
                err_str = err_str + "伤害性质、伤害严重程度和伤害累及系统不符;"   
            if shanghai_xingzhi == '骨折' and shanghai_system != '运动系统':
                target_ws[shanghai_xingzhi_coordinate].fill = pattern
                err_str = err_str + "伤害性质和伤害累及系统不符;"                  
                

            keywords = ['鼻', '肺', '呼吸道']
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_system != '呼吸系统':
                target_ws[shanghai_system_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断和伤害累及系统不符1;"   
                
            keywords = ['口' , '唇' , '下颌' , '消化道' , '酒精中毒']               
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_system != '消化系统':                
                target_ws[shanghai_system_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断和伤害累及系统不符2;"  
                
            keywords = ['生殖器','会阴','外阴','阴唇','阴囊','睾丸','阴茎']               
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_system != '泌尿生殖系统':                    
                target_ws[shanghai_system_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断和伤害累及系统不符3;" 
                                                               
            keywords = ['头部的损伤' , '蛛网膜下腔出血' , '硬膜下血肿' , '脑震荡' , '脑挫裂伤脊髓']               
            if any(keyword in shanghai_zhenduan for keyword in keywords) and shanghai_system != '中枢神经系统':                                                                                                                                  
                target_ws[shanghai_system_coordinate].fill = pattern
                err_str = err_str + "伤害临床诊断和伤害累及系统不符4;"                                 

            if len(err_str):
                target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str)
                target_ws.cell(row=rowIdx, column=maxCol + 1, value=err_str).fill = pattern

            rowIdx = rowIdx + 1


    for i in range(1, target_ws.max_column + 1):
        column_letter = get_column_letter(i)
        target_ws.column_dimensions[column_letter].width = 15
        
    last_column_letter = get_column_letter(target_ws.max_column)
    target_ws.column_dimensions[last_column_letter].width = 50
    
    for row in target_ws.iter_rows():
        target_ws.row_dimensions[row[0].row].height = 18

    for cell in target_wb.active[1]:
        cell.font = Font(bold=True)

    try:
        target_wb.save(target_file)
        print('=' * 50)
        print('✅ 处理完成！')
        print(f'📊 统计信息:')
        print(f'   - 总处理行数: {available_num}')
        print(f'📁 输出文件: {target_file}')
        print('=' * 50)

        print()
        print("🎉 处理完成！")
        print("💡 提示:")
        print("   1. 请查看生成的 result.xlsx 文件")
        print("   2. 黄色高亮显示有问题的数据")
        print("   3. '情况汇总'列显示了具体的错误信息")

    except Exception as e:
        error_msg = f"❌ 处理文件时发生错误: {str(e)}"
        print(error_msg)

if __name__ == "__main__":
    main()

