# Excel伤害数据处理工具 - 代码查看器

这是一个用于展示 `main.py` 代码的网页版查看器，提供了美观的界面和语法高亮功能。

## 📁 文件说明

- `main_code_viewer.html` - 主要的HTML代码查看器文件
- `run_server.py` - Python HTTP服务器启动脚本
- `start_viewer.bat` - Windows批处理启动脚本
- `README.md` - 使用说明文档

## 🚀 使用方法

### 方法1: 直接打开HTML文件（推荐）

1. **双击打开**
   - 直接双击 `main_code_viewer.html` 文件
   - 系统会用默认浏览器打开

2. **右键打开**
   - 右键点击 `main_code_viewer.html`
   - 选择"打开方式" → 选择你喜欢的浏览器

### 方法2: 使用Windows批处理脚本

1. 双击运行 `start_viewer.bat`
2. 脚本会自动：
   - 用默认浏览器打开HTML文件
   - 尝试启动Python HTTP服务器（如果有Python环境）

### 方法3: 使用Python HTTP服务器

1. **确保安装了Python**
   ```bash
   python --version
   ```

2. **运行服务器脚本**
   ```bash
   python run_server.py
   ```

3. **访问网页**
   - 浏览器会自动打开
   - 或手动访问: http://localhost:8000/main_code_viewer.html

### 方法4: 使用Python内置服务器

1. **打开命令行/终端**
   - Windows: 按 `Win + R`，输入 `cmd`
   - Mac/Linux: 打开终端

2. **导航到文件目录**
   ```bash
   cd /path/to/your/files
   ```

3. **启动HTTP服务器**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # 或者 Python 2
   python -m SimpleHTTPServer 8000
   ```

4. **打开浏览器访问**
   ```
   http://localhost:8000/main_code_viewer.html
   ```

## 🌟 功能特性

- ✨ **美观界面** - 现代化的设计风格
- 🎨 **语法高亮** - Python代码语法高亮显示
- 📱 **响应式设计** - 支持手机、平板、电脑访问
- 📋 **一键复制** - 点击按钮复制完整代码
- 🔝 **回到顶部** - 便捷的页面导航
- 📊 **功能说明** - 详细的程序功能介绍

## 🛠️ 技术特点

- 纯HTML/CSS/JavaScript实现
- 无需额外依赖
- 支持所有现代浏览器
- 本地文件即可运行

## 📞 使用提示

1. **最佳体验**: 建议使用Chrome、Firefox、Edge等现代浏览器
2. **复制代码**: 点击"📋 复制代码"按钮可复制完整Python代码
3. **移动端**: 支持手机和平板访问，界面会自动适配
4. **打印**: 支持浏览器打印功能，可生成PDF

## ❓ 常见问题

**Q: 为什么推荐使用HTTP服务器而不是直接打开HTML？**
A: 虽然直接打开HTML文件也能正常工作，但使用HTTP服务器可以：
- 避免某些浏览器的本地文件限制
- 提供更好的字体和样式渲染
- 支持更完整的JavaScript功能

**Q: 如果没有Python环境怎么办？**
A: 直接双击HTML文件即可，无需Python环境。

**Q: 可以修改端口吗？**
A: 可以，编辑 `run_server.py` 文件中的 `PORT = 8000` 行，改为其他端口号。

## 🎉 享受使用！

现在你可以通过美观的网页界面查看和分享你的Python代码了！