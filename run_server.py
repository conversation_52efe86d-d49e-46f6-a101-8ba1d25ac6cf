#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的本地HTTP服务器启动脚本
用于预览HTML文件
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server():
    """启动本地HTTP服务器"""
    
    # 设置端口
    PORT = 8000
    
    # 检查HTML文件是否存在
    html_file = "main_code_viewer.html"
    if not os.path.exists(html_file):
        print(f"❌ 错误: 找不到文件 {html_file}")
        print("请确保HTML文件在当前目录中")
        return
    
    print("🚀 启动本地HTTP服务器...")
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🌐 端口: {PORT}")
    print("=" * 50)
    
    try:
        # 创建HTTP服务器
        Handler = http.server.SimpleHTTPRequestHandler
        
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            server_url = f"http://localhost:{PORT}/{html_file}"
            
            print(f"✅ 服务器启动成功!")
            print(f"🔗 访问地址: {server_url}")
            print()
            print("💡 使用说明:")
            print("1. 服务器正在运行中...")
            print("2. 浏览器将自动打开网页")
            print("3. 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(server_url)
                print("🌐 正在打开浏览器...")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print(f"请手动访问: {server_url}")
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试以下解决方案:")
            print("1. 关闭其他使用该端口的程序")
            print("2. 或者修改脚本中的PORT变量为其他端口")
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        print("感谢使用!")

if __name__ == "__main__":
    start_server()